<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔧 Testing Authentication Flow on Development Server\n";
echo "==================================================\n\n";

// Test 1: Check server configuration
echo "1. Server Configuration:\n";
echo "   - APP_URL: " . config('app.url') . "\n";
echo "   - APP_ENV: " . config('app.env') . "\n";
echo "   - Session Driver: " . config('session.driver') . "\n";
echo "   - Session Domain: " . (config('session.domain') ?: 'null') . "\n";
echo "   - CSRF Protection: " . (config('app.debug') ? 'enabled' : 'enabled') . "\n\n";

// Test 2: Check routes
echo "2. Authentication Routes:\n";
$routes = Route::getRoutes();
foreach ($routes as $route) {
    if (str_contains($route->uri(), 'login')) {
        echo "   - " . implode('|', $route->methods()) . " /" . $route->uri() . " -> " . $route->getActionName() . "\n";
    }
}
echo "\n";

// Test 3: Check database and users
echo "3. Database Status:\n";
try {
    $userCount = \App\Models\User::count();
    $adminUser = \App\Models\User::where('email', '<EMAIL>')->first();
    echo "   ✓ Database connected\n";
    echo "   - Total users: {$userCount}\n";
    echo "   - Admin user: " . ($adminUser ? "✓ Found (ID: {$adminUser->id})" : "✗ Not found") . "\n";

    if ($adminUser) {
        $passwordCheck = \Illuminate\Support\Facades\Hash::check('admin123', $adminUser->password);
        echo "   - Password check: " . ($passwordCheck ? "✓ Valid" : "✗ Invalid") . "\n";
    }
} catch (Exception $e) {
    echo "   ✗ Database error: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 4: Check sessions table
echo "4. Sessions Table:\n";
try {
    $sessionCount = \Illuminate\Support\Facades\DB::table('sessions')->count();
    echo "   ✓ Sessions table accessible\n";
    echo "   - Current sessions: {$sessionCount}\n";
} catch (Exception $e) {
    echo "   ✗ Sessions error: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 5: CSRF Token generation
echo "5. CSRF Token Test:\n";
try {
    // Start session for token generation
    if (!session()->isStarted()) {
        session()->start();
    }
    $token = csrf_token();
    echo "   ✓ CSRF token generated: " . substr($token, 0, 10) . "...\n";
    echo "   - Token length: " . strlen($token) . " characters\n";
    echo "   - Session started: " . (session()->isStarted() ? 'Yes' : 'No') . "\n";
} catch (Exception $e) {
    echo "   ✗ CSRF error: " . $e->getMessage() . "\n";
}
echo "\n";

echo "🚀 Test completed! Server should be running at: http://localhost:8000/login\n";
echo "📝 Admin credentials: <EMAIL> / admin123\n";
echo "🔍 Check browser console for detailed CSRF debugging information.\n";
