<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;
use Illuminate\Support\Facades\Log;
use Illuminate\Session\TokenMismatchException;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array<int, string>
     */
    protected $except = [
        //
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     *
     * @throws \Illuminate\Session\TokenMismatchException
     */
    public function handle($request, \Closure $next)
    {
        // Enhanced CSRF debugging
        if ($request->isMethod('POST') && $request->is('login')) {
            Log::info('CSRF Debug - Login Request', [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'session_id' => $request->session()->getId(),
                'session_token' => $request->session()->token(),
                'request_token' => $request->input('_token'),
                'header_token' => $request->header('X-CSRF-TOKEN'),
                'cookies' => $request->cookies->all(),
                'user_agent' => $request->userAgent(),
                'ip' => $request->ip(),
            ]);
        }

        try {
            return parent::handle($request, $next);
        } catch (TokenMismatchException $e) {
            Log::error('CSRF Token Mismatch', [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'session_id' => $request->session()->getId(),
                'session_token' => $request->session()->token(),
                'request_token' => $request->input('_token'),
                'header_token' => $request->header('X-CSRF-TOKEN'),
                'error' => $e->getMessage(),
            ]);
            
            throw $e;
        }
    }
}
